---
name: Bug report
about: Tell us about a bug you may have identified in CoreUI Free React Admin Template.
title: ''
labels: ''
assignees: ''

---

Before opening:

- [Search for duplicate or closed issues](https://github.com/coreui/coreui-free-react-admin-template/issues?utf8=%E2%9C%93&q=is%3Aissue)
- [Validate](https://html5.validator.nu/) any HTML to avoid common problems
- Read the [contributing guidelines](https://github.com/coreui/coreui-free-react-admin-template/blob/v4-dev/.github/CONTRIBUTING.md)

Bug reports must include:

- Operating system and version (Windows, macOS, Android, iOS)
- Browser and version (Chrome, Firefox, Safari, Microsoft Edge, Opera, Android Browser)
- A [reduced test case](https://css-tricks.com/reduced-test-cases/) or suggested fix using [CodePen](https://codepen.io/) or [JS Bin](https://jsbin.com/)
