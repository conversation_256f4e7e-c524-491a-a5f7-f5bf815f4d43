import { useState, useEffect, useCallback } from 'react';
import { carouselService } from '../services/carouselService';

// Custom hook for managing carousels data
export const useCarousels = () => {
  const [carousels, setCarousels] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Fetch all carousels
  const fetchCarousels = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await carouselService.getAllCarousels();
      
      if (result.success) {
        setCarousels(result.data || []);
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  }, []);

  // Create new carousel
  const createCarousel = useCallback(async (carouselData) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await carouselService.createCarousel(carouselData);
      
      if (result.success) {
        // Refresh the list after creating
        await fetchCarousels();
        return { success: true, data: result.data };
      } else {
        setError(result.error);
        return { success: false, error: result.error };
      }
    } catch (err) {
      const errorMsg = 'Failed to create carousel';
      setError(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  }, [fetchCarousels]);

  // Update carousel
  const updateCarousel = useCallback(async (id, carouselData) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await carouselService.updateCarousel(id, carouselData);
      
      if (result.success) {
        // Refresh the list after updating
        await fetchCarousels();
        return { success: true, data: result.data };
      } else {
        setError(result.error);
        return { success: false, error: result.error };
      }
    } catch (err) {
      const errorMsg = 'Failed to update carousel';
      setError(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  }, [fetchCarousels]);

  // Delete carousel
  const deleteCarousel = useCallback(async (id) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await carouselService.deleteCarousel(id);
      
      if (result.success) {
        // Refresh the list after deleting
        await fetchCarousels();
        return { success: true };
      } else {
        setError(result.error);
        return { success: false, error: result.error };
      }
    } catch (err) {
      const errorMsg = 'Failed to delete carousel';
      setError(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  }, [fetchCarousels]);

  // Load carousels on mount
  useEffect(() => {
    fetchCarousels();
  }, [fetchCarousels]);

  return {
    carousels,
    loading,
    error,
    fetchCarousels,
    createCarousel,
    updateCarousel,
    deleteCarousel,
  };
};

export default useCarousels;
