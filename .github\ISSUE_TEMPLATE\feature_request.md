---
name: Feature request
about: Suggest an idea for a new feature in CoreUI Free React Admin Template.
title: ''
labels: feature
assignees: ''

---

Before opening:

- [Search for duplicate or closed issues](https://github.com/coreui/coreui-free-react-admin-template/issues?utf8=%E2%9C%93&q=is%3Aissue)
- Read the [contributing guidelines](https://github.com/coreui/coreui-free-react-admin-template/blob/main/.github/CONTRIBUTING.md)

Feature requests must include:

- As much detail as possible for what we should add and why it's important to Bootstrap
- Relevant links to prior art, screenshots, or live demos whenever possible
