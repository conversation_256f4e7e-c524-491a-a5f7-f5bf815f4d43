import React, { useState } from 'react'
import {
  CCard,
  CCardBody,
  CCardHeader,
  CCol,
  CRow,
  CTable,
  CTableHead,
  CTableRow,
  CTableHeaderCell,
  CTableBody,
  CTableDataCell,
  CButton,
  CSpinner,
  CAlert,
  CModal,
  CModalHeader,
  CModalTitle,
  CModalBody,
  CModalFooter,
  CForm,
  CFormInput,
  CFormLabel,
  CFormTextarea,
  CBadge,
  CButtonGroup,
  CFormCheck,
} from '@coreui/react'
import CIcon from '@coreui/icons-react'
import {
  cilPlus,
  cilPencil,
  cilTrash,
  cilReload,
  cilEye,
  cilImage
} from '@coreui/icons'
import { useCarousels } from 'src/hooks/useCarousels'

const CarouselsManagement = () => {
  // API integration
  const {
    carousels,
    loading,
    error,
    fetchCarousels,
    createCarousel,
    updateCarousel,
    deleteCarousel
  } = useCarousels()

  // Modal states
  const [showModal, setShowModal] = useState(false)
  const [editingCarousel, setEditingCarousel] = useState(null)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    imageUrl: '',
    isActive: true
  })

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault()

    try {
      if (editingCarousel) {
        await updateCarousel(editingCarousel.id, formData)
      } else {
        await createCarousel(formData)
      }

      // Reset form and close modal
      setFormData({ title: '', description: '', imageUrl: '', isActive: true })
      setEditingCarousel(null)
      setShowModal(false)
    } catch (err) {
      console.error('Error saving carousel:', err)
    }
  }

  // Handle edit
  const handleEdit = (carousel) => {
    setEditingCarousel(carousel)
    setFormData({
      title: carousel.title || '',
      description: carousel.description || '',
      imageUrl: carousel.imageUrl || '',
      isActive: carousel.isActive !== false
    })
    setShowModal(true)
  }

  // Handle delete
  const handleDelete = async (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذا العنصر؟')) {
      await deleteCarousel(id)
    }
  }

  // Handle add new
  const handleAddNew = () => {
    setEditingCarousel(null)
    setFormData({ title: '', description: '', imageUrl: '', isActive: true })
    setShowModal(true)
  }

  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader className="d-flex justify-content-between align-items-center">
            <div>
              <strong>إدارة الـ Carousels</strong>
              <small className="ms-2 text-body-secondary">
                ({carousels.length} عنصر)
              </small>
            </div>
            <CButtonGroup>
              <CButton
                color="primary"
                size="sm"
                onClick={handleAddNew}
              >
                <CIcon icon={cilPlus} className="me-1" />
                إضافة جديد
              </CButton>
              <CButton
                color="secondary"
                size="sm"
                onClick={fetchCarousels}
                disabled={loading}
              >
                <CIcon icon={cilReload} className="me-1" />
                تحديث
              </CButton>
            </CButtonGroup>
          </CCardHeader>
          <CCardBody>
            {loading && (
              <div className="text-center py-4">
                <CSpinner color="primary" />
                <p className="mt-2 text-body-secondary">جاري تحميل البيانات...</p>
              </div>
            )}
            
            {error && (
              <CAlert color="danger" className="mb-3">
                <strong>خطأ:</strong> {error}
              </CAlert>
            )}
            
            {!loading && !error && carousels.length === 0 && (
              <CAlert color="info" className="text-center">
                <CIcon icon={cilImage} size="xl" className="mb-2" />
                <h5>لا توجد بيانات</h5>
                <p>لا توجد عناصر Carousel متاحة. اضغط على "إضافة جديد" لإضافة العنصر الأول.</p>
              </CAlert>
            )}
            
            {!loading && carousels.length > 0 && (
              <div className="table-responsive">
                <CTable hover striped>
                  <CTableHead color="light">
                    <CTableRow>
                      <CTableHeaderCell scope="col">#</CTableHeaderCell>
                      <CTableHeaderCell scope="col">الصورة</CTableHeaderCell>
                      <CTableHeaderCell scope="col">العنوان</CTableHeaderCell>
                      <CTableHeaderCell scope="col">الوصف</CTableHeaderCell>
                      <CTableHeaderCell scope="col">الحالة</CTableHeaderCell>
                      <CTableHeaderCell scope="col" className="text-center">الإجراءات</CTableHeaderCell>
                    </CTableRow>
                  </CTableHead>
                  <CTableBody>
                    {carousels.map((carousel, index) => (
                      <CTableRow key={carousel.id || index}>
                        <CTableDataCell>{index + 1}</CTableDataCell>
                        <CTableDataCell>
                          {carousel.imageUrl ? (
                            <img 
                              src={carousel.imageUrl} 
                              alt={carousel.title} 
                              style={{ 
                                width: '60px', 
                                height: '40px', 
                                objectFit: 'cover',
                                borderRadius: '4px'
                              }}
                              onError={(e) => {
                                e.target.style.display = 'none';
                                e.target.nextSibling.style.display = 'block';
                              }}
                            />
                          ) : null}
                          <div
                            style={{
                              display: carousel.imageUrl ? 'none' : 'flex',
                              width: '60px',
                              height: '40px',
                              backgroundColor: '#f8f9fa',
                              border: '1px dashed #dee2e6',
                              borderRadius: '4px',
                              alignItems: 'center',
                              justifyContent: 'center',
                              fontSize: '12px',
                              color: '#6c757d'
                            }}
                          >
                            لا توجد صورة
                          </div>
                        </CTableDataCell>
                        <CTableDataCell>
                          <strong>{carousel.title || 'بدون عنوان'}</strong>
                        </CTableDataCell>
                        <CTableDataCell>
                          {carousel.description ? 
                            (carousel.description.length > 60 ? 
                              carousel.description.substring(0, 60) + '...' : 
                              carousel.description
                            ) : 
                            <span className="text-muted">بدون وصف</span>
                          }
                        </CTableDataCell>
                        <CTableDataCell>
                          <CBadge 
                            color={carousel.isActive !== false ? 'success' : 'secondary'}
                            shape="rounded-pill"
                          >
                            {carousel.isActive !== false ? 'نشط' : 'غير نشط'}
                          </CBadge>
                        </CTableDataCell>
                        <CTableDataCell className="text-center">
                          <CButtonGroup size="sm">
                            <CButton 
                              color="info" 
                              variant="ghost"
                              onClick={() => handleView(carousel)}
                              title="عرض"
                            >
                              <CIcon icon={cilEye} />
                            </CButton>
                            <CButton 
                              color="warning" 
                              variant="ghost"
                              onClick={() => handleEdit(carousel)}
                              title="تعديل"
                            >
                              <CIcon icon={cilPencil} />
                            </CButton>
                            <CButton 
                              color="danger" 
                              variant="ghost"
                              onClick={() => handleDelete(carousel.id)}
                              title="حذف"
                            >
                              <CIcon icon={cilTrash} />
                            </CButton>
                          </CButtonGroup>
                        </CTableDataCell>
                      </CTableRow>
                    ))}
                  </CTableBody>
                </CTable>
              </div>
            )}
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default CarouselsManagement
