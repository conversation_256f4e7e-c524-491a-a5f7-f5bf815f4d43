import React, { useState } from 'react'
import {
  CCard,
  CCardBody,
  CCardHeader,
  CCol,
  CRow,
  CTable,
  CTableHead,
  CTableRow,
  CTableHeaderCell,
  CTableBody,
  CTableDataCell,
  CButton,
  CSpinner,
  CAlert,
  CModal,
  CModalHeader,
  CModalTitle,
  CModalBody,
  CModalFooter,
  CForm,
  CFormInput,
  CFormLabel,
  CFormTextarea,
  CBadge,
  CButtonGroup,
  CFormCheck,
} from '@coreui/react'
import CIcon from '@coreui/icons-react'
import {
  cilPlus,
  cilPencil,
  cilTrash,
  cilReload,
  cilEye,
  cilImage
} from '@coreui/icons'
import { useCarousels } from 'src/hooks/useCarousels'

const CarouselsManagement = () => {
  // API integration
  const {
    carousels,
    loading,
    error,
    fetchCarousels,
    createCarousel,
    updateCarousel,
    deleteCarousel
  } = useCarousels()

  // Modal states
  const [showModal, setShowModal] = useState(false)
  const [editingCarousel, setEditingCarousel] = useState(null)
  const [formData, setFormData] = useState({
    descriptionAr: '',
    descriptionEn: '',
    categoryId: 0,
    imagePath: '',
    showOrder: 0
  })

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault()

    try {
      if (editingCarousel) {
        await updateCarousel(editingCarousel.id, formData)
      } else {
        await createCarousel(formData)
      }

      // Reset form and close modal
      setFormData({ descriptionAr: '', descriptionEn: '', categoryId: 0, imagePath: '', showOrder: 0 })
      setEditingCarousel(null)
      setShowModal(false)
    } catch (err) {
      console.error('Error saving carousel:', err)
    }
  }

  // Handle edit
  const handleEdit = (carousel) => {
    setEditingCarousel(carousel)
    setFormData({
      descriptionAr: carousel.descriptionAr || '',
      descriptionEn: carousel.descriptionEn || '',
      categoryId: carousel.categoryId || 0,
      imagePath: carousel.imagePath || '',
      showOrder: carousel.showOrder || 0
    })
    setShowModal(true)
  }

  // Handle delete
  const handleDelete = async (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذا العنصر؟')) {
      await deleteCarousel(id)
    }
  }

  // Handle add new
  const handleAddNew = () => {
    setEditingCarousel(null)
    setFormData({ descriptionAr: '', descriptionEn: '', categoryId: 0, imagePath: '', showOrder: 0 })
    setShowModal(true)
  }

  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader className="d-flex justify-content-between align-items-center">
            <div>
              <strong>إدارة الـ Carousels</strong>
              <small className="ms-2 text-body-secondary">
                ({carousels.length} عنصر)
              </small>
            </div>
            <CButtonGroup>
              <CButton
                color="primary"
                size="sm"
                onClick={handleAddNew}
              >
                <CIcon icon={cilPlus} className="me-1" />
                إضافة جديد
              </CButton>
              <CButton
                color="secondary"
                size="sm"
                onClick={fetchCarousels}
                disabled={loading}
              >
                <CIcon icon={cilReload} className="me-1" />
                تحديث
              </CButton>
            </CButtonGroup>
          </CCardHeader>
          <CCardBody>
            {loading && (
              <div className="text-center py-4">
                <CSpinner color="primary" />
                <p className="mt-2 text-body-secondary">جاري تحميل البيانات...</p>
              </div>
            )}
            
            {error && (
              <CAlert color="danger" className="mb-3">
                <strong>خطأ:</strong> {error}
              </CAlert>
            )}
            
            {!loading && !error && carousels.length === 0 && (
              <CAlert color="info" className="text-center">
                <CIcon icon={cilImage} size="xl" className="mb-2" />
                <h5>لا توجد بيانات</h5>
                <p>لا توجد عناصر Carousel متاحة. اضغط على "إضافة جديد" لإضافة العنصر الأول.</p>
              </CAlert>
            )}
            
            {!loading && carousels.length > 0 && (
              <div className="table-responsive">
                <CTable hover striped>
                  <CTableHead color="light">
                    <CTableRow>
                      <CTableHeaderCell scope="col">#</CTableHeaderCell>
                      <CTableHeaderCell scope="col">الصورة</CTableHeaderCell>
                      <CTableHeaderCell scope="col">الوصف العربي</CTableHeaderCell>
                      <CTableHeaderCell scope="col">الوصف الإنجليزي</CTableHeaderCell>
                      <CTableHeaderCell scope="col">رقم الفئة</CTableHeaderCell>
                      <CTableHeaderCell scope="col">ترتيب العرض</CTableHeaderCell>
                      <CTableHeaderCell scope="col" className="text-center">الإجراءات</CTableHeaderCell>
                    </CTableRow>
                  </CTableHead>
                  <CTableBody>
                    {carousels.map((carousel, index) => (
                      <CTableRow key={carousel.id || index}>
                        <CTableDataCell>{index + 1}</CTableDataCell>
                        <CTableDataCell>
                          {carousel.imagePath ? (
                            <img
                              src={carousel.imagePath}
                              alt={carousel.descriptionAr || 'صورة'}
                              style={{
                                width: '60px',
                                height: '40px',
                                objectFit: 'cover',
                                borderRadius: '4px'
                              }}
                              onError={(e) => {
                                e.target.style.display = 'none';
                                e.target.nextSibling.style.display = 'block';
                              }}
                            />
                          ) : null}
                          <div
                            style={{
                              display: carousel.imagePath ? 'none' : 'flex',
                              width: '60px',
                              height: '40px',
                              backgroundColor: '#f8f9fa',
                              border: '1px dashed #dee2e6',
                              borderRadius: '4px',
                              alignItems: 'center',
                              justifyContent: 'center',
                              fontSize: '12px',
                              color: '#6c757d'
                            }}
                          >
                            لا توجد صورة
                          </div>
                        </CTableDataCell>
                        <CTableDataCell>
                          {carousel.descriptionAr ?
                            (carousel.descriptionAr.length > 50 ?
                              carousel.descriptionAr.substring(0, 50) + '...' :
                              carousel.descriptionAr
                            ) :
                            <span className="text-muted">بدون وصف</span>
                          }
                        </CTableDataCell>
                        <CTableDataCell>
                          {carousel.descriptionEn ?
                            (carousel.descriptionEn.length > 50 ?
                              carousel.descriptionEn.substring(0, 50) + '...' :
                              carousel.descriptionEn
                            ) :
                            <span className="text-muted">No description</span>
                          }
                        </CTableDataCell>
                        <CTableDataCell>
                          <CBadge color="info" shape="rounded-pill">
                            {carousel.categoryId || 0}
                          </CBadge>
                        </CTableDataCell>
                        <CTableDataCell>
                          <CBadge color="secondary" shape="rounded-pill">
                            {carousel.showOrder || 0}
                          </CBadge>
                        </CTableDataCell>
                        <CTableDataCell className="text-center">
                          <CButtonGroup size="sm">
                            <CButton
                              color="warning"
                              variant="ghost"
                              onClick={() => handleEdit(carousel)}
                              title="تعديل"
                            >
                              <CIcon icon={cilPencil} />
                            </CButton>
                            <CButton
                              color="danger"
                              variant="ghost"
                              onClick={() => handleDelete(carousel.id)}
                              title="حذف"
                            >
                              <CIcon icon={cilTrash} />
                            </CButton>
                          </CButtonGroup>
                        </CTableDataCell>
                      </CTableRow>
                    ))}
                  </CTableBody>
                </CTable>
              </div>
            )}
          </CCardBody>
        </CCard>
      </CCol>

      {/* Add/Edit Modal */}
      <CModal visible={showModal} onClose={() => setShowModal(false)} size="lg">
        <CModalHeader>
          <CModalTitle>
            {editingCarousel ? 'تعديل Carousel' : 'إضافة Carousel جديد'}
          </CModalTitle>
        </CModalHeader>
        <CForm onSubmit={handleSubmit}>
          <CModalBody>
            <CRow>
              <CCol md={6}>
                <div className="mb-3">
                  <CFormLabel htmlFor="descriptionAr">الوصف العربي *</CFormLabel>
                  <CFormTextarea
                    id="descriptionAr"
                    rows={3}
                    value={formData.descriptionAr}
                    onChange={(e) => setFormData({ ...formData, descriptionAr: e.target.value })}
                    placeholder="أدخل الوصف باللغة العربية"
                    required
                  />
                </div>
              </CCol>
              <CCol md={6}>
                <div className="mb-3">
                  <CFormLabel htmlFor="descriptionEn">الوصف الإنجليزي *</CFormLabel>
                  <CFormTextarea
                    id="descriptionEn"
                    rows={3}
                    value={formData.descriptionEn}
                    onChange={(e) => setFormData({ ...formData, descriptionEn: e.target.value })}
                    placeholder="Enter description in English"
                    required
                  />
                </div>
              </CCol>
            </CRow>

            <CRow>
              <CCol md={4}>
                <div className="mb-3">
                  <CFormLabel htmlFor="categoryId">رقم الفئة *</CFormLabel>
                  <CFormInput
                    type="number"
                    id="categoryId"
                    value={formData.categoryId}
                    onChange={(e) => setFormData({ ...formData, categoryId: parseInt(e.target.value) || 0 })}
                    placeholder="0"
                    min="0"
                    required
                  />
                </div>
              </CCol>
              <CCol md={4}>
                <div className="mb-3">
                  <CFormLabel htmlFor="showOrder">ترتيب العرض *</CFormLabel>
                  <CFormInput
                    type="number"
                    id="showOrder"
                    value={formData.showOrder}
                    onChange={(e) => setFormData({ ...formData, showOrder: parseInt(e.target.value) || 0 })}
                    placeholder="0"
                    min="0"
                    required
                  />
                </div>
              </CCol>
              <CCol md={4}>
                <div className="mb-3">
                  <CFormLabel htmlFor="imagePath">مسار الصورة</CFormLabel>
                  <CFormInput
                    type="text"
                    id="imagePath"
                    value={formData.imagePath}
                    onChange={(e) => setFormData({ ...formData, imagePath: e.target.value })}
                    placeholder="path/to/image.jpg"
                  />
                </div>
              </CCol>
            </CRow>

            {formData.imagePath && (
              <div className="mb-3">
                <CFormLabel>معاينة الصورة</CFormLabel>
                <div className="border rounded p-2">
                  <img
                    src={formData.imagePath}
                    alt="Preview"
                    style={{
                      width: '100%',
                      maxHeight: '200px',
                      objectFit: 'cover',
                      borderRadius: '4px'
                    }}
                    onError={(e) => {
                      e.target.style.display = 'none';
                      e.target.nextSibling.style.display = 'block';
                    }}
                  />
                  <div
                    style={{
                      display: 'none',
                      padding: '20px',
                      textAlign: 'center',
                      color: '#dc3545',
                      backgroundColor: '#f8d7da',
                      border: '1px solid #f5c6cb',
                      borderRadius: '4px'
                    }}
                  >
                    خطأ في تحميل الصورة
                  </div>
                </div>
              </div>
            )}
          </CModalBody>
          <CModalFooter>
            <CButton
              color="secondary"
              onClick={() => setShowModal(false)}
              disabled={loading}
            >
              إلغاء
            </CButton>
            <CButton color="primary" type="submit" disabled={loading}>
              {loading ? (
                <>
                  <CSpinner size="sm" className="me-2" />
                  جاري الحفظ...
                </>
              ) : (
                editingCarousel ? 'تحديث' : 'إضافة'
              )}
            </CButton>
          </CModalFooter>
        </CForm>
      </CModal>
    </CRow>
  )
}

export default CarouselsManagement
