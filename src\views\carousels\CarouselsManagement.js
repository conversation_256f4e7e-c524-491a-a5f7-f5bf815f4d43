import React, { useState } from 'react'
import {
  CCard,
  CCardBody,
  CCardHeader,
  CCol,
  CRow,
  CTable,
  CTableHead,
  CTableRow,
  CTableHeaderCell,
  CTableBody,
  CTableDataCell,
  CButton,
  CSpinner,
  CAlert,
  CModal,
  CModalHeader,
  CModalTitle,
  CModalBody,
  CModalFooter,
  CForm,
  CFormInput,
  CFormLabel,
  CFormTextarea,
  CBadge,
  CButtonGroup,
  CFormCheck,
} from '@coreui/react'
import CIcon from '@coreui/icons-react'
import { 
  cilPlus, 
  cilPencil, 
  cilTrash, 
  cilReload,
  cilEye,
  cilImage
} from '@coreui/icons'
import { useCarousels } from 'src/hooks/useCarousels'

const CarouselsManagement = () => {
  // API integration
  const { 
    carousels, 
    loading, 
    error, 
    fetchCarousels, 
    createCarousel, 
    updateCarousel, 
    deleteCarousel 
  } = useCarousels()
  
  // Modal states
  const [showModal, setShowModal] = useState(false)
  const [showViewModal, setShowViewModal] = useState(false)
  const [editingCarousel, setEditingCarousel] = useState(null)
  const [viewingCarousel, setViewingCarousel] = useState(null)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    imageUrl: '',
    isActive: true
  })

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault()
    
    try {
      if (editingCarousel) {
        await updateCarousel(editingCarousel.id, formData)
      } else {
        await createCarousel(formData)
      }
      
      // Reset form and close modal
      setFormData({ title: '', description: '', imageUrl: '', isActive: true })
      setEditingCarousel(null)
      setShowModal(false)
    } catch (err) {
      console.error('Error saving carousel:', err)
    }
  }

  // Handle edit
  const handleEdit = (carousel) => {
    setEditingCarousel(carousel)
    setFormData({
      title: carousel.title || '',
      description: carousel.description || '',
      imageUrl: carousel.imageUrl || '',
      isActive: carousel.isActive !== false
    })
    setShowModal(true)
  }

  // Handle view
  const handleView = (carousel) => {
    setViewingCarousel(carousel)
    setShowViewModal(true)
  }

  // Handle delete
  const handleDelete = async (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذا العنصر؟')) {
      await deleteCarousel(id)
    }
  }

  // Handle add new
  const handleAddNew = () => {
    setEditingCarousel(null)
    setFormData({ title: '', description: '', imageUrl: '', isActive: true })
    setShowModal(true)
  }

  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader className="d-flex justify-content-between align-items-center">
            <div>
              <strong>إدارة الـ Carousels</strong>
              <small className="ms-2 text-body-secondary">
                ({carousels.length} عنصر)
              </small>
            </div>
            <CButtonGroup>
              <CButton 
                color="primary" 
                size="sm"
                onClick={handleAddNew}
              >
                <CIcon icon={cilPlus} className="me-1" />
                إضافة جديد
              </CButton>
              <CButton 
                color="secondary" 
                size="sm" 
                onClick={fetchCarousels}
                disabled={loading}
              >
                <CIcon icon={cilReload} className="me-1" />
                تحديث
              </CButton>
            </CButtonGroup>
          </CCardHeader>
          <CCardBody>
            {loading && (
              <div className="text-center py-4">
                <CSpinner color="primary" />
                <p className="mt-2 text-body-secondary">جاري تحميل البيانات...</p>
              </div>
            )}
            
            {error && (
              <CAlert color="danger" className="mb-3">
                <strong>خطأ:</strong> {error}
              </CAlert>
            )}
            
            {!loading && !error && carousels.length === 0 && (
              <CAlert color="info" className="text-center">
                <CIcon icon={cilImage} size="xl" className="mb-2" />
                <h5>لا توجد بيانات</h5>
                <p>لا توجد عناصر Carousel متاحة. اضغط على "إضافة جديد" لإضافة العنصر الأول.</p>
              </CAlert>
            )}
            
            {!loading && carousels.length > 0 && (
              <div className="table-responsive">
                <CTable hover striped>
                  <CTableHead color="light">
                    <CTableRow>
                      <CTableHeaderCell scope="col">#</CTableHeaderCell>
                      <CTableHeaderCell scope="col">الصورة</CTableHeaderCell>
                      <CTableHeaderCell scope="col">العنوان</CTableHeaderCell>
                      <CTableHeaderCell scope="col">الوصف</CTableHeaderCell>
                      <CTableHeaderCell scope="col">الحالة</CTableHeaderCell>
                      <CTableHeaderCell scope="col" className="text-center">الإجراءات</CTableHeaderCell>
                    </CTableRow>
                  </CTableHead>
                  <CTableBody>
                    {carousels.map((carousel, index) => (
                      <CTableRow key={carousel.id || index}>
                        <CTableDataCell>{index + 1}</CTableDataCell>
                        <CTableDataCell>
                          {carousel.imageUrl ? (
                            <img 
                              src={carousel.imageUrl} 
                              alt={carousel.title} 
                              style={{ 
                                width: '60px', 
                                height: '40px', 
                                objectFit: 'cover',
                                borderRadius: '4px'
                              }}
                              onError={(e) => {
                                e.target.style.display = 'none';
                                e.target.nextSibling.style.display = 'block';
                              }}
                            />
                          ) : null}
                          <div
                            style={{
                              display: carousel.imageUrl ? 'none' : 'flex',
                              width: '60px',
                              height: '40px',
                              backgroundColor: '#f8f9fa',
                              border: '1px dashed #dee2e6',
                              borderRadius: '4px',
                              alignItems: 'center',
                              justifyContent: 'center',
                              fontSize: '12px',
                              color: '#6c757d'
                            }}
                          >
                            لا توجد صورة
                          </div>
                        </CTableDataCell>
                        <CTableDataCell>
                          <strong>{carousel.title || 'بدون عنوان'}</strong>
                        </CTableDataCell>
                        <CTableDataCell>
                          {carousel.description ? 
                            (carousel.description.length > 60 ? 
                              carousel.description.substring(0, 60) + '...' : 
                              carousel.description
                            ) : 
                            <span className="text-muted">بدون وصف</span>
                          }
                        </CTableDataCell>
                        <CTableDataCell>
                          <CBadge 
                            color={carousel.isActive !== false ? 'success' : 'secondary'}
                            shape="rounded-pill"
                          >
                            {carousel.isActive !== false ? 'نشط' : 'غير نشط'}
                          </CBadge>
                        </CTableDataCell>
                        <CTableDataCell className="text-center">
                          <CButtonGroup size="sm">
                            <CButton 
                              color="info" 
                              variant="ghost"
                              onClick={() => handleView(carousel)}
                              title="عرض"
                            >
                              <CIcon icon={cilEye} />
                            </CButton>
                            <CButton 
                              color="warning" 
                              variant="ghost"
                              onClick={() => handleEdit(carousel)}
                              title="تعديل"
                            >
                              <CIcon icon={cilPencil} />
                            </CButton>
                            <CButton 
                              color="danger" 
                              variant="ghost"
                              onClick={() => handleDelete(carousel.id)}
                              title="حذف"
                            >
                              <CIcon icon={cilTrash} />
                            </CButton>
                          </CButtonGroup>
                        </CTableDataCell>
                      </CTableRow>
                    ))}
                  </CTableBody>
                </CTable>
              </div>
            )}
          </CCardBody>
        </CCard>
      </CCol>

      {/* Add/Edit Modal */}
      <CModal visible={showModal} onClose={() => setShowModal(false)} size="lg">
        <CModalHeader>
          <CModalTitle>
            {editingCarousel ? 'تعديل Carousel' : 'إضافة Carousel جديد'}
          </CModalTitle>
        </CModalHeader>
        <CForm onSubmit={handleSubmit}>
          <CModalBody>
            <CRow>
              <CCol md={6}>
                <div className="mb-3">
                  <CFormLabel htmlFor="title">العنوان *</CFormLabel>
                  <CFormInput
                    type="text"
                    id="title"
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    placeholder="أدخل عنوان الـ Carousel"
                    required
                  />
                </div>
              </CCol>
              <CCol md={6}>
                <div className="mb-3">
                  <CFormLabel htmlFor="imageUrl">رابط الصورة</CFormLabel>
                  <CFormInput
                    type="url"
                    id="imageUrl"
                    value={formData.imageUrl}
                    onChange={(e) => setFormData({ ...formData, imageUrl: e.target.value })}
                    placeholder="https://example.com/image.jpg"
                  />
                </div>
              </CCol>
            </CRow>

            <div className="mb-3">
              <CFormLabel htmlFor="description">الوصف</CFormLabel>
              <CFormTextarea
                id="description"
                rows={4}
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="أدخل وصف الـ Carousel"
              />
            </div>

            {formData.imageUrl && (
              <div className="mb-3">
                <CFormLabel>معاينة الصورة</CFormLabel>
                <div className="border rounded p-2">
                  <img
                    src={formData.imageUrl}
                    alt="Preview"
                    style={{
                      width: '100%',
                      maxHeight: '200px',
                      objectFit: 'cover',
                      borderRadius: '4px'
                    }}
                    onError={(e) => {
                      e.target.style.display = 'none';
                      e.target.nextSibling.style.display = 'block';
                    }}
                  />
                  <div
                    style={{
                      display: 'none',
                      padding: '20px',
                      textAlign: 'center',
                      color: '#dc3545',
                      backgroundColor: '#f8d7da',
                      border: '1px solid #f5c6cb',
                      borderRadius: '4px'
                    }}
                  >
                    خطأ في تحميل الصورة
                  </div>
                </div>
              </div>
            )}

            <div className="mb-3">
              <CFormCheck
                id="isActive"
                checked={formData.isActive}
                onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                label="نشط"
              />
            </div>
          </CModalBody>
          <CModalFooter>
            <CButton
              color="secondary"
              onClick={() => setShowModal(false)}
              disabled={loading}
            >
              إلغاء
            </CButton>
            <CButton color="primary" type="submit" disabled={loading}>
              {loading ? (
                <>
                  <CSpinner size="sm" className="me-2" />
                  جاري الحفظ...
                </>
              ) : (
                editingCarousel ? 'تحديث' : 'إضافة'
              )}
            </CButton>
          </CModalFooter>
        </CForm>
      </CModal>

      {/* View Modal */}
      <CModal visible={showViewModal} onClose={() => setShowViewModal(false)} size="lg">
        <CModalHeader>
          <CModalTitle>عرض تفاصيل Carousel</CModalTitle>
        </CModalHeader>
        <CModalBody>
          {viewingCarousel && (
            <CRow>
              <CCol md={6}>
                <div className="mb-3">
                  <strong>العنوان:</strong>
                  <p className="mt-1">{viewingCarousel.title || 'بدون عنوان'}</p>
                </div>
                <div className="mb-3">
                  <strong>الحالة:</strong>
                  <div className="mt-1">
                    <CBadge
                      color={viewingCarousel.isActive !== false ? 'success' : 'secondary'}
                      shape="rounded-pill"
                    >
                      {viewingCarousel.isActive !== false ? 'نشط' : 'غير نشط'}
                    </CBadge>
                  </div>
                </div>
                {viewingCarousel.description && (
                  <div className="mb-3">
                    <strong>الوصف:</strong>
                    <p className="mt-1">{viewingCarousel.description}</p>
                  </div>
                )}
              </CCol>
              <CCol md={6}>
                {viewingCarousel.imageUrl && (
                  <div className="mb-3">
                    <strong>الصورة:</strong>
                    <div className="mt-1">
                      <img
                        src={viewingCarousel.imageUrl}
                        alt={viewingCarousel.title}
                        style={{
                          width: '100%',
                          maxHeight: '300px',
                          objectFit: 'cover',
                          borderRadius: '8px',
                          border: '1px solid #dee2e6'
                        }}
                        onError={(e) => {
                          e.target.style.display = 'none';
                          e.target.nextSibling.style.display = 'block';
                        }}
                      />
                      <div
                        style={{
                          display: 'none',
                          padding: '40px',
                          textAlign: 'center',
                          color: '#6c757d',
                          backgroundColor: '#f8f9fa',
                          border: '1px dashed #dee2e6',
                          borderRadius: '8px'
                        }}
                      >
                        لا يمكن تحميل الصورة
                      </div>
                    </div>
                  </div>
                )}
              </CCol>
            </CRow>
          )}
        </CModalBody>
        <CModalFooter>
          <CButton color="secondary" onClick={() => setShowViewModal(false)}>
            إغلاق
          </CButton>
        </CModalFooter>
      </CModal>
    </CRow>
  )
}

export default CarouselsManagement
