import apiClient from './api';

// Carousel API endpoints
const CAROUSEL_ENDPOINTS = {
  getAll: '/Carousels',
  getById: (id) => `/Carousels/${id}`,
  create: '/Carousels',
  update: (id) => `/Carousels/${id}`,
  delete: (id) => `/Carousels/${id}`,
};

// Carousel service functions
export const carouselService = {
  // Get all carousels
  getAllCarousels: async () => {
    try {
      const response = await apiClient.get(CAROUSEL_ENDPOINTS.getAll);
      return {
        success: true,
        data: response.data,
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Failed to fetch carousels',
        status: error.response?.status,
      };
    }
  },

  // Get carousel by ID
  getCarouselById: async (id) => {
    try {
      const response = await apiClient.get(CAROUSEL_ENDPOINTS.getById(id));
      return {
        success: true,
        data: response.data,
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Failed to fetch carousel',
        status: error.response?.status,
      };
    }
  },

  // Create new carousel
  createCarousel: async (carouselData) => {
    try {
      const response = await apiClient.post(CAROUSEL_ENDPOINTS.create, carouselData);
      return {
        success: true,
        data: response.data,
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Failed to create carousel',
        status: error.response?.status,
      };
    }
  },

  // Update carousel
  updateCarousel: async (id, carouselData) => {
    try {
      const response = await apiClient.put(CAROUSEL_ENDPOINTS.update(id), carouselData);
      return {
        success: true,
        data: response.data,
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Failed to update carousel',
        status: error.response?.status,
      };
    }
  },

  // Delete carousel
  deleteCarousel: async (id) => {
    try {
      const response = await apiClient.delete(CAROUSEL_ENDPOINTS.delete(id));
      return {
        success: true,
        data: response.data,
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Failed to delete carousel',
        status: error.response?.status,
      };
    }
  },
};

export default carouselService;
