import React, { useState } from 'react'
import {
  CCard,
  CCardBody,
  CCardHeader,
  CCarousel,
  CCarouselCaption,
  CCarouselItem,
  CCol,
  CRow,
  CSpinner,
  CAlert,
  CButton,
  CModal,
  CModalHeader,
  CModalTitle,
  CModalBody,
  CModalFooter,
  CForm,
  CFormInput,
  CFormLabel,
  CFormTextarea,
  CTable,
  CTableHead,
  CTableRow,
  CTableHeaderCell,
  CTableBody,
  CTableDataCell,
  CBadge,
} from '@coreui/react'
import CIcon from '@coreui/icons-react'
import { cilPlus, cilPencil, cilTrash, cilReload } from '@coreui/icons'
import { DocsComponents, DocsExample } from 'src/components'
import { useCarousels } from 'src/hooks/useCarousels'

import AngularImg from 'src/assets/images/angular.jpg'
import ReactImg from 'src/assets/images/react.jpg'
import VueImg from 'src/assets/images/vue.jpg'

const slidesLight = [
  'data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%22800%22%20height%3D%22400%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20800%20400%22%20preserveAspectRatio%3D%22none%22%3E%3Cdefs%3E%3Cstyle%20type%3D%22text%2Fcss%22%3E%23holder_1607923e7e2%20text%20%7B%20fill%3A%23AAA%3Bfont-weight%3Anormal%3Bfont-family%3AHelvetica%2C%20monospace%3Bfont-size%3A40pt%20%7D%20%3C%2Fstyle%3E%3C%2Fdefs%3E%3Cg%20id%3D%22holder_1607923e7e2%22%3E%3Crect%20width%3D%22800%22%20height%3D%22400%22%20fill%3D%22%23F5F5F5%22%3E%3C%2Frect%3E%3Cg%3E%3Ctext%20x%3D%22285.9296875%22%20y%3D%22217.75625%22%3EFirst%20slide%3C%2Ftext%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E',
  'data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%22800%22%20height%3D%22400%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20800%20400%22%20preserveAspectRatio%3D%22none%22%3E%3Cdefs%3E%3Cstyle%20type%3D%22text%2Fcss%22%3E%23holder_15ba800aa20%20text%20%7B%20fill%3A%23BBB%3Bfont-weight%3Anormal%3Bfont-family%3AHelvetica%2C%20monospace%3Bfont-size%3A40pt%20%7D%20%3C%2Fstyle%3E%3C%2Fdefs%3E%3Cg%20id%3D%22holder_15ba800aa20%22%3E%3Crect%20width%3D%22800%22%20height%3D%22400%22%20fill%3D%22%23EEE%22%3E%3C%2Frect%3E%3Cg%3E%3Ctext%20x%3D%22247.3203125%22%20y%3D%22218.3%22%3ESecond%20slide%3C%2Ftext%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E',
  'data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%22800%22%20height%3D%22400%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20800%20400%22%20preserveAspectRatio%3D%22none%22%3E%3Cdefs%3E%3Cstyle%20type%3D%22text%2Fcss%22%3E%23holder_15ba800aa21%20text%20%7B%20fill%3A%23999%3Bfont-weight%3Anormal%3Bfont-family%3AHelvetica%2C%20monospace%3Bfont-size%3A40pt%20%7D%20%3C%2Fstyle%3E%3C%2Fdefs%3E%3Cg%20id%3D%22holder_15ba800aa21%22%3E%3Crect%20width%3D%22800%22%20height%3D%22400%22%20fill%3D%22%23E5E5E5%22%3E%3C%2Frect%3E%3Cg%3E%3Ctext%20x%3D%22277%22%20y%3D%22218.3%22%3EThird%20slide%3C%2Ftext%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E',
]

const Carousels = () => {
  // API integration
  const { carousels, loading, error, fetchCarousels, createCarousel, updateCarousel, deleteCarousel } = useCarousels()

  // Modal states
  const [showModal, setShowModal] = useState(false)
  const [editingCarousel, setEditingCarousel] = useState(null)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    imageUrl: '',
    isActive: true
  })

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault()

    try {
      if (editingCarousel) {
        await updateCarousel(editingCarousel.id, formData)
      } else {
        await createCarousel(formData)
      }

      // Reset form and close modal
      setFormData({ title: '', description: '', imageUrl: '', isActive: true })
      setEditingCarousel(null)
      setShowModal(false)
    } catch (err) {
      console.error('Error saving carousel:', err)
    }
  }

  // Handle edit
  const handleEdit = (carousel) => {
    setEditingCarousel(carousel)
    setFormData({
      title: carousel.title || '',
      description: carousel.description || '',
      imageUrl: carousel.imageUrl || '',
      isActive: carousel.isActive !== false
    })
    setShowModal(true)
  }

  // Handle delete
  const handleDelete = async (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذا العنصر؟')) {
      await deleteCarousel(id)
    }
  }

  // Handle add new
  const handleAddNew = () => {
    setEditingCarousel(null)
    setFormData({ title: '', description: '', imageUrl: '', isActive: true })
    setShowModal(true)
  }
  return (
    <CRow>
      <CCol xs={12}>
        <DocsComponents href="components/carousel/" />

        {/* API Data Management Section */}
        <CCard className="mb-4">
          <CCardHeader className="d-flex justify-content-between align-items-center">
            <div>
              <strong>إدارة الـ Carousels</strong> <small>البيانات من API</small>
            </div>
            <div>
              <CButton
                color="primary"
                size="sm"
                className="me-2"
                onClick={handleAddNew}
              >
                <CIcon icon={cilPlus} className="me-1" />
                إضافة جديد
              </CButton>
              <CButton
                color="secondary"
                size="sm"
                onClick={fetchCarousels}
                disabled={loading}
              >
                <CIcon icon={cilReload} className="me-1" />
                تحديث
              </CButton>
            </div>
          </CCardHeader>
          <CCardBody>
            {loading && (
              <div className="text-center py-3">
                <CSpinner color="primary" />
                <p className="mt-2">جاري تحميل البيانات...</p>
              </div>
            )}

            {error && (
              <CAlert color="danger" className="mb-3">
                <strong>خطأ:</strong> {error}
              </CAlert>
            )}

            {!loading && !error && carousels.length === 0 && (
              <CAlert color="info">
                لا توجد بيانات متاحة. اضغط على "إضافة جديد" لإضافة عنصر جديد.
              </CAlert>
            )}

            {!loading && carousels.length > 0 && (
              <CTable hover responsive>
                <CTableHead>
                  <CTableRow>
                    <CTableHeaderCell>العنوان</CTableHeaderCell>
                    <CTableHeaderCell>الوصف</CTableHeaderCell>
                    <CTableHeaderCell>الصورة</CTableHeaderCell>
                    <CTableHeaderCell>الحالة</CTableHeaderCell>
                    <CTableHeaderCell>الإجراءات</CTableHeaderCell>
                  </CTableRow>
                </CTableHead>
                <CTableBody>
                  {carousels.map((carousel, index) => (
                    <CTableRow key={carousel.id || index}>
                      <CTableDataCell>{carousel.title || 'بدون عنوان'}</CTableDataCell>
                      <CTableDataCell>
                        {carousel.description ?
                          (carousel.description.length > 50 ?
                            carousel.description.substring(0, 50) + '...' :
                            carousel.description
                          ) : 'بدون وصف'
                        }
                      </CTableDataCell>
                      <CTableDataCell>
                        {carousel.imageUrl ? (
                          <img
                            src={carousel.imageUrl}
                            alt={carousel.title}
                            style={{ width: '50px', height: '30px', objectFit: 'cover' }}
                            onError={(e) => {
                              e.target.src = ReactImg; // fallback image
                            }}
                          />
                        ) : (
                          <span className="text-muted">لا توجد صورة</span>
                        )}
                      </CTableDataCell>
                      <CTableDataCell>
                        <CBadge color={carousel.isActive !== false ? 'success' : 'secondary'}>
                          {carousel.isActive !== false ? 'نشط' : 'غير نشط'}
                        </CBadge>
                      </CTableDataCell>
                      <CTableDataCell>
                        <CButton
                          color="info"
                          size="sm"
                          className="me-2"
                          onClick={() => handleEdit(carousel)}
                        >
                          <CIcon icon={cilPencil} />
                        </CButton>
                        <CButton
                          color="danger"
                          size="sm"
                          onClick={() => handleDelete(carousel.id)}
                        >
                          <CIcon icon={cilTrash} />
                        </CButton>
                      </CTableDataCell>
                    </CTableRow>
                  ))}
                </CTableBody>
              </CTable>
            )}
          </CCardBody>
        </CCard>

        {/* API Data Carousel Display */}
        {!loading && carousels.length > 0 && (
          <CCard className="mb-4">
            <CCardHeader>
              <strong>عرض البيانات من API</strong> <small>Carousel من البيانات الحقيقية</small>
            </CCardHeader>
            <CCardBody>
              <CCarousel controls indicators>
                {carousels.map((carousel, index) => (
                  <CCarouselItem key={carousel.id || index}>
                    <img
                      className="d-block w-100"
                      src={carousel.imageUrl || ReactImg}
                      alt={carousel.title || `slide ${index + 1}`}
                      style={{ height: '400px', objectFit: 'cover' }}
                      onError={(e) => {
                        e.target.src = ReactImg; // fallback image
                      }}
                    />
                    {(carousel.title || carousel.description) && (
                      <CCarouselCaption className="d-none d-md-block">
                        {carousel.title && <h5>{carousel.title}</h5>}
                        {carousel.description && <p>{carousel.description}</p>}
                      </CCarouselCaption>
                    )}
                  </CCarouselItem>
                ))}
              </CCarousel>
            </CCardBody>
          </CCard>
        )}

        <CCard className="mb-4">
          <CCardHeader>
            <strong>Carousel</strong> <small>Slide only</small>
          </CCardHeader>
          <CCardBody>
            <p className="text-body-secondary small">Here’s a carousel with slides</p>
            <DocsExample href="components/carousel">
              <CCarousel>
                <CCarouselItem>
                  <img className="d-block w-100" src={ReactImg} alt="slide 1" />
                </CCarouselItem>
                <CCarouselItem>
                  <img className="d-block w-100" src={AngularImg} alt="slide 2" />
                </CCarouselItem>
                <CCarouselItem>
                  <img className="d-block w-100" src={VueImg} alt="slide 3" />
                </CCarouselItem>
              </CCarousel>
            </DocsExample>
          </CCardBody>
        </CCard>
      </CCol>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <strong>Carousel</strong> <small>With controls</small>
          </CCardHeader>
          <CCardBody>
            <p className="text-body-secondary small">
              Adding in the previous and next controls by <code>controls</code> property.
            </p>
            <DocsExample href="components/carousel/#with-controls">
              <CCarousel controls>
                <CCarouselItem>
                  <img className="d-block w-100" src={ReactImg} alt="slide 1" />
                </CCarouselItem>
                <CCarouselItem>
                  <img className="d-block w-100" src={AngularImg} alt="slide 2" />
                </CCarouselItem>
                <CCarouselItem>
                  <img className="d-block w-100" src={VueImg} alt="slide 3" />
                </CCarouselItem>
              </CCarousel>
            </DocsExample>
          </CCardBody>
        </CCard>
      </CCol>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <strong>Carousel</strong> <small>With indicators</small>
          </CCardHeader>
          <CCardBody>
            <p className="text-body-secondary small">
              You can attach the indicators to the carousel, lengthwise the controls, too.
            </p>
            <DocsExample href="components/carousel/#with-indicators">
              <CCarousel controls indicators>
                <CCarouselItem>
                  <img className="d-block w-100" src={ReactImg} alt="slide 1" />
                </CCarouselItem>
                <CCarouselItem>
                  <img className="d-block w-100" src={AngularImg} alt="slide 2" />
                </CCarouselItem>
                <CCarouselItem>
                  <img className="d-block w-100" src={VueImg} alt="slide 3" />
                </CCarouselItem>
              </CCarousel>
            </DocsExample>
          </CCardBody>
        </CCard>
      </CCol>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <strong>Carousel</strong> <small>With captions</small>
          </CCardHeader>
          <CCardBody>
            <p className="text-body-secondary small">
              You can add captions to slides with the <code>&lt;CCarouselCaption&gt;</code> element
              within any <code>&lt;CCarouselItem&gt;</code>. They can be immediately hidden on
              smaller viewports, as shown below, with optional{' '}
              <a href="https://coreui.io//utilities/display">display utilities</a>. We hide them
              with <code>.d-none</code> and draw them back on medium-sized devices with{' '}
              <code>.d-md-block</code>.
            </p>
            <DocsExample href="components/carousel/#with-captions">
              <CCarousel controls indicators>
                <CCarouselItem>
                  <img className="d-block w-100" src={ReactImg} alt="slide 1" />
                  <CCarouselCaption className="d-none d-md-block">
                    <h5>First slide label</h5>
                    <p>Some representative placeholder content for the first slide.</p>
                  </CCarouselCaption>
                </CCarouselItem>
                <CCarouselItem>
                  <img className="d-block w-100" src={AngularImg} alt="slide 2" />
                  <CCarouselCaption className="d-none d-md-block">
                    <h5>Second slide label</h5>
                    <p>Some representative placeholder content for the first slide.</p>
                  </CCarouselCaption>
                </CCarouselItem>
                <CCarouselItem>
                  <img className="d-block w-100" src={VueImg} alt="slide 3" />
                  <CCarouselCaption className="d-none d-md-block">
                    <h5>Third slide label</h5>
                    <p>Some representative placeholder content for the first slide.</p>
                  </CCarouselCaption>
                </CCarouselItem>
              </CCarousel>
            </DocsExample>
          </CCardBody>
        </CCard>
      </CCol>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <strong>Carousel</strong> <small>Crossfade</small>
          </CCardHeader>
          <CCardBody>
            <p className="text-body-secondary small">
              Add <code>transition=&#34;crossfade&#34;</code> to your carousel to animate slides
              with a fade transition instead of a slide.
            </p>
            <DocsExample href="components/carousel/#crossfade">
              <CCarousel controls transition="crossfade">
                <CCarouselItem>
                  <img className="d-block w-100" src={ReactImg} alt="slide 1" />
                </CCarouselItem>
                <CCarouselItem>
                  <img className="d-block w-100" src={AngularImg} alt="slide 2" />
                </CCarouselItem>
                <CCarouselItem>
                  <img className="d-block w-100" src={VueImg} alt="slide 3" />
                </CCarouselItem>
              </CCarousel>
            </DocsExample>
          </CCardBody>
        </CCard>
      </CCol>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <strong>Carousel</strong> <small>Dark variant</small>
          </CCardHeader>
          <CCardBody>
            <p className="text-body-secondary small">
              Add <code>dark</code> property to the <code>CCarousel</code> for darker controls,
              indicators, and captions. Controls have been inverted from their default white fill
              with the <code>filter</code> CSS property. Captions and controls have additional Sass
              variables that customize the <code>color</code> and <code>background-color</code>.
            </p>
            <DocsExample href="components/carousel/#dark-variant">
              <CCarousel controls indicators dark>
                <CCarouselItem>
                  <img className="d-block w-100" src={slidesLight[0]} alt="slide 1" />
                  <CCarouselCaption className="d-none d-md-block">
                    <h5>First slide label</h5>
                    <p>Some representative placeholder content for the first slide.</p>
                  </CCarouselCaption>
                </CCarouselItem>
                <CCarouselItem>
                  <img className="d-block w-100" src={slidesLight[1]} alt="slide 2" />
                  <CCarouselCaption className="d-none d-md-block">
                    <h5>Second slide label</h5>
                    <p>Some representative placeholder content for the first slide.</p>
                  </CCarouselCaption>
                </CCarouselItem>
                <CCarouselItem>
                  <img className="d-block w-100" src={slidesLight[2]} alt="slide 3" />
                  <CCarouselCaption className="d-none d-md-block">
                    <h5>Third slide label</h5>
                    <p>Some representative placeholder content for the first slide.</p>
                  </CCarouselCaption>
                </CCarouselItem>
              </CCarousel>
            </DocsExample>
          </CCardBody>
        </CCard>
      </CCol>

      {/* Add/Edit Modal */}
      <CModal visible={showModal} onClose={() => setShowModal(false)} size="lg">
        <CModalHeader>
          <CModalTitle>
            {editingCarousel ? 'تعديل Carousel' : 'إضافة Carousel جديد'}
          </CModalTitle>
        </CModalHeader>
        <CForm onSubmit={handleSubmit}>
          <CModalBody>
            <div className="mb-3">
              <CFormLabel htmlFor="title">العنوان</CFormLabel>
              <CFormInput
                type="text"
                id="title"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                placeholder="أدخل عنوان الـ Carousel"
                required
              />
            </div>
            <div className="mb-3">
              <CFormLabel htmlFor="description">الوصف</CFormLabel>
              <CFormTextarea
                id="description"
                rows={3}
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="أدخل وصف الـ Carousel"
              />
            </div>
            <div className="mb-3">
              <CFormLabel htmlFor="imageUrl">رابط الصورة</CFormLabel>
              <CFormInput
                type="url"
                id="imageUrl"
                value={formData.imageUrl}
                onChange={(e) => setFormData({ ...formData, imageUrl: e.target.value })}
                placeholder="https://example.com/image.jpg"
              />
              {formData.imageUrl && (
                <div className="mt-2">
                  <img
                    src={formData.imageUrl}
                    alt="Preview"
                    style={{ width: '100px', height: '60px', objectFit: 'cover' }}
                    onError={(e) => {
                      e.target.style.display = 'none';
                    }}
                  />
                </div>
              )}
            </div>
            <div className="mb-3">
              <div className="form-check">
                <input
                  className="form-check-input"
                  type="checkbox"
                  id="isActive"
                  checked={formData.isActive}
                  onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                />
                <label className="form-check-label" htmlFor="isActive">
                  نشط
                </label>
              </div>
            </div>
          </CModalBody>
          <CModalFooter>
            <CButton color="secondary" onClick={() => setShowModal(false)}>
              إلغاء
            </CButton>
            <CButton color="primary" type="submit" disabled={loading}>
              {loading ? (
                <>
                  <CSpinner size="sm" className="me-2" />
                  جاري الحفظ...
                </>
              ) : (
                editingCarousel ? 'تحديث' : 'إضافة'
              )}
            </CButton>
          </CModalFooter>
        </CForm>
      </CModal>
    </CRow>
  )
}

export default Carousels
